<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paiements - Gestion Fourrière</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            min-height: 100vh;
        }

        /* Menu vertical */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 1rem;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .user-info {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .menu-item {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .menu-item:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .menu-item.active {
            background-color: rgba(255,255,255,0.2);
            border-right: 3px solid white;
        }

        .menu-item i {
            margin-right: 0.5rem;
            width: 20px;
        }

        /* Contenu principal */
        .main-content {
            margin-left: 250px;
            flex: 1;
            padding: 2rem;
        }

        .header {
            background: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        /* Cartes de paiement */
        .payment-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .payment-section h2 {
            color: #333;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f0f0f0;
        }

        /* Recherche de véhicule */
        .search-section {
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        /* Informations véhicule */
        .vehicle-info {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: none;
        }

        .vehicle-info.show {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .info-item {
            background: white;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .info-item label {
            font-size: 0.8rem;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 0.25rem;
            display: block;
        }

        .info-item .value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }

        /* Formulaire de paiement */
        .payment-form {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        /* Tableau des paiements */
        .table-container {
            overflow-x: auto;
            margin-top: 2rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status.valide {
            background-color: #d4edda;
            color: #155724;
        }

        .status.en-attente {
            background-color: #fff3cd;
            color: #856404;
        }

        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .amount-highlight {
            font-size: 1.2rem;
            font-weight: bold;
            color: #dc3545;
        }

        .amount-paid {
            color: #28a745;
        }
    </style>
</head>
<body>
    <!-- Menu vertical -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h2>🚗 Gestion Fourrière</h2>
            <div class="user-info" id="userInfo">
                Chargement...
            </div>
        </div>
        
        <a href="?page=dashboard" class="menu-item">
            <i>📊</i> Dashboard
        </a>
        <a href="?page=entrees-sorties" class="menu-item">
            <i>🚗</i> Entrées/Sorties
        </a>
        <a href="?page=paiements" class="menu-item active">
            <i>💰</i> Paiements
        </a>
        <a href="?page=parametres" class="menu-item">
            <i>⚙️</i> Paramètres
        </a>
    </nav>

    <!-- Contenu principal -->
    <main class="main-content">
        <header class="header">
            <h1>Gestion des Paiements</h1>
            <button class="logout-btn" onclick="deconnecter()">
                Déconnexion
            </button>
        </header>

        <!-- Messages -->
        <div id="messageContainer"></div>

        <!-- Section nouveau paiement -->
        <section class="payment-section">
            <h2>💳 Nouveau Paiement</h2>
            
            <div class="search-section">
                <div class="form-group">
                    <label for="rechercheVehicule">Rechercher un véhicule (par immatriculation)</label>
                    <input type="text" id="rechercheVehicule" placeholder="Tapez l'immatriculation..." 
                           style="text-transform: uppercase;">
                </div>
            </div>

            <!-- Informations du véhicule -->
            <div id="vehicleInfo" class="vehicle-info">
                <h3>Informations du véhicule</h3>
                <div class="info-grid" id="vehicleDetails">
                    <!-- Détails du véhicule seront insérés ici -->
                </div>

                <!-- Formulaire de paiement -->
                <div class="payment-form">
                    <h4>Enregistrer un paiement</h4>
                    <form id="paiementForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="montantPaiement">Montant du paiement (€) *</label>
                                <input type="number" id="montantPaiement" name="montant" 
                                       step="0.01" min="0" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="methodePaiement">Méthode de paiement *</label>
                                <select id="methodePaiement" name="methode" required>
                                    <option value="">Sélectionner une méthode</option>
                                    <option value="Espèces">Espèces</option>
                                    <option value="Carte bancaire">Carte bancaire</option>
                                    <option value="Chèque">Chèque</option>
                                    <option value="Virement">Virement bancaire</option>
                                </select>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-success" style="margin-top: 1rem;">
                            Enregistrer le paiement
                        </button>
                    </form>
                </div>
            </div>
        </section>

        <!-- Section historique des paiements -->
        <section class="payment-section">
            <h2>📋 Historique des Paiements</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Immatriculation</th>
                            <th>Montant</th>
                            <th>Méthode</th>
                            <th>Statut</th>
                            <th>Utilisateur</th>
                        </tr>
                    </thead>
                    <tbody id="paiementsTableBody">
                        <tr>
                            <td colspan="6" class="loading">Chargement des données...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <script>
        let vehiculesData = [];
        let vehiculeSelectionne = null;

        // Vérifier l'authentification
        function verifierAuth() {
            const user = sessionStorage.getItem('user');
            if (!user) {
                window.location.href = '?page=login';
                return null;
            }
            return JSON.parse(user);
        }

        // Déconnexion
        function deconnecter() {
            sessionStorage.removeItem('user');
            window.location.href = '?page=login';
        }

        // Afficher un message
        function afficherMessage(message, type = 'success') {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `
                <div class="alert alert-${type}">
                    ${message}
                </div>
            `;
            
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Recherche de véhicule
        document.getElementById('rechercheVehicule').addEventListener('input', function(e) {
            const immatriculation = e.target.value.toUpperCase();
            
            if (immatriculation.length >= 3) {
                const vehicule = vehiculesData.find(v => 
                    v.immatriculation.includes(immatriculation)
                );
                
                if (vehicule) {
                    afficherVehicule(vehicule);
                } else {
                    masquerVehicule();
                }
            } else {
                masquerVehicule();
            }
        });

        // Afficher les informations du véhicule
        function afficherVehicule(vehicule) {
            vehiculeSelectionne = vehicule;
            const dateEntree = new Date(vehicule.dateEntree);
            const dateSortie = vehicule.dateSortie ? new Date(vehicule.dateSortie) : new Date();
            const nbJours = Math.ceil((dateSortie - dateEntree) / (1000 * 60 * 60 * 24));
            const montantRestant = (vehicule.montantDu || 0) - (vehicule.montantPaye || 0);
            
            document.getElementById('vehicleDetails').innerHTML = `
                <div class="info-item">
                    <label>Immatriculation</label>
                    <div class="value">${vehicule.immatriculation}</div>
                </div>
                <div class="info-item">
                    <label>Type</label>
                    <div class="value">${vehicule.type}</div>
                </div>
                <div class="info-item">
                    <label>Marque/Modèle</label>
                    <div class="value">${vehicule.marque} ${vehicule.modele}</div>
                </div>
                <div class="info-item">
                    <label>Date d'entrée</label>
                    <div class="value">${dateEntree.toLocaleDateString('fr-FR')}</div>
                </div>
                <div class="info-item">
                    <label>Nombre de jours</label>
                    <div class="value">${nbJours}</div>
                </div>
                <div class="info-item">
                    <label>Statut</label>
                    <div class="value">${vehicule.statut}</div>
                </div>
                <div class="info-item">
                    <label>Montant total dû</label>
                    <div class="value amount-highlight">${(vehicule.montantDu || 0).toFixed(2)} €</div>
                </div>
                <div class="info-item">
                    <label>Montant payé</label>
                    <div class="value amount-paid">${(vehicule.montantPaye || 0).toFixed(2)} €</div>
                </div>
                <div class="info-item">
                    <label>Montant restant</label>
                    <div class="value ${montantRestant > 0 ? 'amount-highlight' : 'amount-paid'}">
                        ${montantRestant.toFixed(2)} €
                    </div>
                </div>
            `;
            
            // Pré-remplir le montant du paiement avec le montant restant
            document.getElementById('montantPaiement').value = montantRestant > 0 ? montantRestant.toFixed(2) : '';
            
            document.getElementById('vehicleInfo').classList.add('show');
            
            if (montantRestant <= 0) {
                afficherMessage('Ce véhicule n\'a plus de montant à payer.', 'warning');
            }
        }

        // Masquer les informations du véhicule
        function masquerVehicule() {
            vehiculeSelectionne = null;
            document.getElementById('vehicleInfo').classList.remove('show');
        }

        // Gérer le formulaire de paiement
        document.getElementById('paiementForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!vehiculeSelectionne) {
                afficherMessage('Veuillez sélectionner un véhicule.', 'danger');
                return;
            }
            
            const formData = new FormData(e.target);
            const montant = parseFloat(formData.get('montant'));
            const methode = formData.get('methode');
            
            if (montant <= 0) {
                afficherMessage('Le montant doit être supérieur à 0.', 'danger');
                return;
            }
            
            const montantRestant = (vehiculeSelectionne.montantDu || 0) - (vehiculeSelectionne.montantPaye || 0);
            if (montant > montantRestant) {
                afficherMessage(`Le montant ne peut pas dépasser le montant restant (${montantRestant.toFixed(2)} €).`, 'danger');
                return;
            }
            
            google.script.run
                .withSuccessHandler(function(result) {
                    if (result.success) {
                        afficherMessage('Paiement enregistré avec succès!', 'success');
                        e.target.reset();
                        masquerVehicule();
                        document.getElementById('rechercheVehicule').value = '';
                        chargerVehicules();
                        chargerPaiements();
                    } else {
                        afficherMessage('Erreur: ' + result.message, 'danger');
                    }
                })
                .withFailureHandler(function(error) {
                    afficherMessage('Erreur lors de l\'enregistrement: ' + error.message, 'danger');
                })
                .enregistrerPaiement(vehiculeSelectionne.id, montant, methode);
        });

        // Charger les véhicules
        function chargerVehicules() {
            google.script.run
                .withSuccessHandler(function(result) {
                    if (result.success) {
                        vehiculesData = result.data;
                    }
                })
                .withFailureHandler(function(error) {
                    console.error('Erreur lors du chargement des véhicules:', error);
                })
                .obtenirVehicules();
        }

        // Charger les paiements (fonction simulée - à implémenter dans Code.gs)
        function chargerPaiements() {
            // Cette fonction devrait appeler une méthode Google Apps Script pour obtenir l'historique des paiements
            const tbody = document.getElementById('paiementsTableBody');
            tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">Fonctionnalité en cours de développement</td></tr>';
        }

        // Initialisation
        window.onload = function() {
            const user = verifierAuth();
            if (user) {
                document.getElementById('userInfo').textContent = user.nom + ' (' + user.role + ')';
                chargerVehicules();
                chargerPaiements();
            }
        };
    </script>
</body>
</html>
