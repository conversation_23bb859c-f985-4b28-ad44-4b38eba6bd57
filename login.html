<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion Fourrière - Connexion</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #666;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn-login {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn-login:hover {
            transform: translateY(-2px);
        }

        .btn-login:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 1rem;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #667eea;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            color: #666;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🚗 Gestion Fourrière</h1>
            <p>Connectez-vous pour accéder au système</p>
        </div>

        <div id="errorMessage" class="error-message"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email :</label>
                <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
                <label for="password">Mot de passe :</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" id="loginBtn" class="btn-login">
                Se connecter
            </button>
        </form>

        <div id="loading" class="loading"></div>

        <div class="footer">
            <p>&copy; 2024 Système de Gestion de Fourrière</p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('errorMessage');
            const loadingDiv = document.getElementById('loading');
            const loginBtn = document.getElementById('loginBtn');
            
            // Réinitialiser l'affichage
            errorDiv.style.display = 'none';
            loadingDiv.style.display = 'block';
            loginBtn.disabled = true;
            loginBtn.textContent = 'Connexion en cours...';
            
            // Appel à la fonction Google Apps Script
            google.script.run
                .withSuccessHandler(function(result) {
                    loadingDiv.style.display = 'none';
                    loginBtn.disabled = false;
                    loginBtn.textContent = 'Se connecter';
                    
                    if (result.success) {
                        // Stocker les informations utilisateur
                        sessionStorage.setItem('user', JSON.stringify(result.user));
                        // Rediriger vers le dashboard
                        window.location.href = '?page=dashboard';
                    } else {
                        errorDiv.textContent = result.message;
                        errorDiv.style.display = 'block';
                    }
                })
                .withFailureHandler(function(error) {
                    loadingDiv.style.display = 'none';
                    loginBtn.disabled = false;
                    loginBtn.textContent = 'Se connecter';
                    errorDiv.textContent = 'Erreur de connexion: ' + error.message;
                    errorDiv.style.display = 'block';
                })
                .authentifierUtilisateur(email, password);
        });

        // Vérifier si l'utilisateur est déjà connecté
        window.onload = function() {
            const user = sessionStorage.getItem('user');
            if (user) {
                window.location.href = '?page=dashboard';
            }
        };
    </script>
</body>
</html>
