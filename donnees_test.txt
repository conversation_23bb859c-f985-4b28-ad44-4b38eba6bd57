# Données de Test pour Google Sheets

## <PERSON><PERSON><PERSON> "Véhicules" - Ligne 1 (En-tê<PERSON>)
ID	Immatriculation	Type	Marque	Modele	Date_Entree	Date_Sortie	Statut	Montant_Du	Montant_Paye

## Feuille "Véhicules" - Donn<PERSON> de test (lignes 2-5)
v001	AB-123-CD	Voiture	Peugeot	308	2024-01-15		En fourrière	150	0
v002	EF-456-GH	Moto	Yamaha	MT-07	2024-01-16		En fourrière	75	0
v003	IJ-789-KL	Camion	Renault	Master	2024-01-17	2024-01-20	Sorti	300	300
v004	MN-012-OP	Voiture	Citroën	C3	2024-01-18		En fourrière	100	50

## Feuille "Utilisateurs" - Ligne 1 (En-têtes)
ID	Email	Mot_De_Passe	Nom	Actif	Role

## Feuille "Utilisateurs" - Données de test (lignes 2-4)
u001	<EMAIL>	admin123	Administrateur	TRUE	Admin
u002	<EMAIL>	agent123	Agent Martin	TRUE	Agent
u003	<EMAIL>	agent456	Agent Dubois	TRUE	Agent

## Fe<PERSON>le "Types_Véhicules" - Ligne 1 (En-têtes)
Type	Tarif_Journalier	Description

## Feuille "Types_Véhicules" - Données de test (lignes 2-7)
Voiture	50	Véhicule léger standard
Moto	25	Deux roues motorisé
Camion	100	Véhicule utilitaire lourd
Camionnette	75	Véhicule utilitaire léger
Bus	150	Transport en commun
Remorque	30	Remorque ou caravane

## Feuille "Paiements" - Ligne 1 (En-têtes)
ID	Vehicule_ID	Montant	Methode_Paiement	Date_Paiement	Statut

## Feuille "Paiements" - Données de test (lignes 2-3)
p001	v003	300	Espèces	2024-01-20	Validé
p002	v004	50	Carte bancaire	2024-01-19	Validé

## Feuille "Paramètres" - Ligne 1 (En-têtes)
Parametre	Valeur	Description

## Feuille "Paramètres" - Données de test (lignes 2-6)
Tarif_Minimum	25	Tarif minimum par jour
Delai_Grace	1	Nombre de jours de grâce
Taux_Penalite	10	Pourcentage de pénalité par jour de retard
Devise	EUR	Devise utilisée
Nom_Fourriere	Fourrière Municipale	Nom de l'établissement

# Instructions de Copie

1. Copiez chaque section d'en-têtes dans la ligne 1 de chaque feuille
2. Copiez les données de test dans les lignes suivantes
3. Assurez-vous que les colonnes sont bien alignées
4. Les dates doivent être au format YYYY-MM-DD
5. Les valeurs TRUE/FALSE doivent être en majuscules
6. Les montants sont en nombres (pas de symbole €)

# Vérification Rapide

Après avoir ajouté les données :
- Feuille "Véhicules" : 5 lignes (1 en-tête + 4 véhicules)
- Feuille "Utilisateurs" : 4 lignes (1 en-tête + 3 utilisateurs)
- Feuille "Types_Véhicules" : 7 lignes (1 en-tête + 6 types)
- Feuille "Paiements" : 3 lignes (1 en-tête + 2 paiements)
- Feuille "Paramètres" : 6 lignes (1 en-tête + 5 paramètres)
