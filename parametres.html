<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paramètres - Gestion Fourrière</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            min-height: 100vh;
        }

        /* Menu vertical */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 1rem;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .user-info {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .menu-item {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .menu-item:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .menu-item.active {
            background-color: rgba(255,255,255,0.2);
            border-right: 3px solid white;
        }

        .menu-item i {
            margin-right: 0.5rem;
            width: 20px;
        }

        /* Contenu principal */
        .main-content {
            margin-left: 250px;
            flex: 1;
            padding: 2rem;
        }

        .header {
            background: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        /* Onglets */
        .tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .tab.active {
            background: #667eea;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #e9ecef;
        }

        /* Contenu des onglets */
        .tab-content {
            display: none;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        .tab-content h2 {
            color: #333;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f0f0f0;
        }

        /* Formulaires */
        .form-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .form-section h3 {
            color: #333;
            margin-bottom: 1rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-right: 0.5rem;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        /* Tableaux */
        .table-container {
            overflow-x: auto;
            margin-top: 1rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .actions {
            display: flex;
            gap: 0.5rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-badge.actif {
            background-color: #d4edda;
            color: #155724;
        }

        .status-badge.inactif {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- Menu vertical -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h2>🚗 Gestion Fourrière</h2>
            <div class="user-info" id="userInfo">
                Chargement...
            </div>
        </div>
        
        <a href="?page=dashboard" class="menu-item">
            <i>📊</i> Dashboard
        </a>
        <a href="?page=entrees-sorties" class="menu-item">
            <i>🚗</i> Entrées/Sorties
        </a>
        <a href="?page=paiements" class="menu-item">
            <i>💰</i> Paiements
        </a>
        <a href="?page=parametres" class="menu-item active">
            <i>⚙️</i> Paramètres
        </a>
    </nav>

    <!-- Contenu principal -->
    <main class="main-content">
        <header class="header">
            <h1>Paramètres du Système</h1>
            <button class="logout-btn" onclick="deconnecter()">
                Déconnexion
            </button>
        </header>

        <!-- Messages -->
        <div id="messageContainer"></div>

        <!-- Onglets -->
        <div class="tabs">
            <button class="tab active" onclick="changerOnglet('types-vehicules')">
                Types de Véhicules
            </button>
            <button class="tab" onclick="changerOnglet('utilisateurs')">
                Utilisateurs
            </button>
            <button class="tab" onclick="changerOnglet('parametres-generaux')">
                Paramètres Généraux
            </button>
        </div>

        <!-- Contenu Types de Véhicules -->
        <div id="types-vehicules" class="tab-content active">
            <h2>🚗 Gestion des Types de Véhicules</h2>
            
            <div class="form-section">
                <h3>Ajouter un nouveau type</h3>
                <form id="typeVehiculeForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="nomType">Nom du type *</label>
                            <input type="text" id="nomType" name="nom" required placeholder="Ex: Voiture">
                        </div>
                        
                        <div class="form-group">
                            <label for="tarifJournalier">Tarif journalier (€) *</label>
                            <input type="number" id="tarifJournalier" name="tarif" step="0.01" min="0" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="descriptionType">Description</label>
                            <textarea id="descriptionType" name="description" rows="3" 
                                      placeholder="Description du type de véhicule"></textarea>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        Ajouter le type
                    </button>
                </form>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Tarif journalier</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="typesTableBody">
                        <tr>
                            <td colspan="4" class="loading">Chargement des données...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Contenu Utilisateurs -->
        <div id="utilisateurs" class="tab-content">
            <h2>👥 Gestion des Utilisateurs</h2>
            
            <div class="form-section">
                <h3>Ajouter un nouvel utilisateur</h3>
                <form id="utilisateurForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="emailUtilisateur">Email *</label>
                            <input type="email" id="emailUtilisateur" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="motDePasseUtilisateur">Mot de passe *</label>
                            <input type="password" id="motDePasseUtilisateur" name="motDePasse" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="nomUtilisateur">Nom complet *</label>
                            <input type="text" id="nomUtilisateur" name="nom" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="roleUtilisateur">Rôle *</label>
                            <select id="roleUtilisateur" name="role" required>
                                <option value="">Sélectionner un rôle</option>
                                <option value="Admin">Administrateur</option>
                                <option value="Agent">Agent</option>
                                <option value="Superviseur">Superviseur</option>
                            </select>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        Ajouter l'utilisateur
                    </button>
                </form>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Email</th>
                            <th>Nom</th>
                            <th>Rôle</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="utilisateursTableBody">
                        <tr>
                            <td colspan="5" class="loading">Chargement des données...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Contenu Paramètres Généraux -->
        <div id="parametres-generaux" class="tab-content">
            <h2>⚙️ Paramètres Généraux</h2>
            
            <div class="form-section">
                <h3>Configuration du système</h3>
                <form id="parametresForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="nomFourriere">Nom de la fourrière</label>
                            <input type="text" id="nomFourriere" name="nomFourriere" 
                                   placeholder="Fourrière Municipale">
                        </div>
                        
                        <div class="form-group">
                            <label for="tarifMinimum">Tarif minimum (€)</label>
                            <input type="number" id="tarifMinimum" name="tarifMinimum" 
                                   step="0.01" min="0" placeholder="25">
                        </div>
                        
                        <div class="form-group">
                            <label for="delaiGrace">Délai de grâce (jours)</label>
                            <input type="number" id="delaiGrace" name="delaiGrace" 
                                   min="0" placeholder="1">
                        </div>
                        
                        <div class="form-group">
                            <label for="tauxPenalite">Taux de pénalité (%)</label>
                            <input type="number" id="tauxPenalite" name="tauxPenalite" 
                                   step="0.1" min="0" placeholder="10">
                        </div>
                        
                        <div class="form-group">
                            <label for="devise">Devise</label>
                            <select id="devise" name="devise">
                                <option value="EUR">Euro (€)</option>
                                <option value="USD">Dollar ($)</option>
                                <option value="MAD">Dirham (DH)</option>
                            </select>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-success">
                        Sauvegarder les paramètres
                    </button>
                </form>
            </div>
        </div>
    </main>

    <script>
        // Vérifier l'authentification
        function verifierAuth() {
            const user = sessionStorage.getItem('user');
            if (!user) {
                window.location.href = '?page=login';
                return null;
            }
            return JSON.parse(user);
        }

        // Déconnexion
        function deconnecter() {
            sessionStorage.removeItem('user');
            window.location.href = '?page=login';
        }

        // Changer d'onglet
        function changerOnglet(onglet) {
            // Masquer tous les contenus
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Désactiver tous les onglets
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Activer l'onglet sélectionné
            document.getElementById(onglet).classList.add('active');
            event.target.classList.add('active');
            
            // Charger les données si nécessaire
            if (onglet === 'types-vehicules') {
                chargerTypesVehicules();
            } else if (onglet === 'utilisateurs') {
                chargerUtilisateurs();
            } else if (onglet === 'parametres-generaux') {
                chargerParametres();
            }
        }

        // Afficher un message
        function afficherMessage(message, type = 'success') {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `
                <div class="alert alert-${type}">
                    ${message}
                </div>
            `;
            
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Charger les types de véhicules
        function chargerTypesVehicules() {
            google.script.run
                .withSuccessHandler(function(result) {
                    const tbody = document.getElementById('typesTableBody');
                    
                    if (result.success) {
                        const types = result.data;
                        
                        if (types.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;">Aucun type trouvé</td></tr>';
                            return;
                        }
                        
                        tbody.innerHTML = types.map(type => `
                            <tr>
                                <td>${type.nom}</td>
                                <td>${type.tarifJournalier.toFixed(2)} €</td>
                                <td>${type.description || '-'}</td>
                                <td class="actions">
                                    <button class="btn btn-danger btn-sm" onclick="supprimerType('${type.nom}')">
                                        Supprimer
                                    </button>
                                </td>
                            </tr>
                        `).join('');
                    } else {
                        tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: red;">Erreur: ' + result.message + '</td></tr>';
                    }
                })
                .withFailureHandler(function(error) {
                    document.getElementById('typesTableBody').innerHTML = 
                        '<tr><td colspan="4" style="text-align: center; color: red;">Erreur de chargement: ' + error.message + '</td></tr>';
                })
                .obtenirTypesVehicules();
        }

        // Charger les utilisateurs (fonction simulée)
        function chargerUtilisateurs() {
            const tbody = document.getElementById('utilisateursTableBody');
            tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">Fonctionnalité en cours de développement</td></tr>';
        }

        // Charger les paramètres (fonction simulée)
        function chargerParametres() {
            // Pré-remplir avec des valeurs par défaut
            document.getElementById('nomFourriere').value = 'Fourrière Municipale';
            document.getElementById('tarifMinimum').value = '25';
            document.getElementById('delaiGrace').value = '1';
            document.getElementById('tauxPenalite').value = '10';
            document.getElementById('devise').value = 'EUR';
        }

        // Gérer le formulaire de type de véhicule
        document.getElementById('typeVehiculeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            afficherMessage('Fonctionnalité en cours de développement', 'warning');
        });

        // Gérer le formulaire d'utilisateur
        document.getElementById('utilisateurForm').addEventListener('submit', function(e) {
            e.preventDefault();
            afficherMessage('Fonctionnalité en cours de développement', 'warning');
        });

        // Gérer le formulaire de paramètres
        document.getElementById('parametresForm').addEventListener('submit', function(e) {
            e.preventDefault();
            afficherMessage('Paramètres sauvegardés avec succès!', 'success');
        });

        // Supprimer un type (fonction simulée)
        function supprimerType(nom) {
            if (confirm(`Êtes-vous sûr de vouloir supprimer le type "${nom}"?`)) {
                afficherMessage('Fonctionnalité en cours de développement', 'warning');
            }
        }

        // Initialisation
        window.onload = function() {
            const user = verifierAuth();
            if (user) {
                document.getElementById('userInfo').textContent = user.nom + ' (' + user.role + ')';
                chargerTypesVehicules();
            }
        };
    </script>
</body>
</html>
