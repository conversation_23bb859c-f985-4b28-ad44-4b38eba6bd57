/**
 * Application de Gestion de Fourrière
 * Fichier principal Google Apps Script
 */

// Configuration globale
const CONFIG = {
  SPREADSHEET_ID: '13vYDBbh5tM-StV6KJcrakpwf5wJj-qHlhQhEnF3G7Cc', // À remplacer par l'ID de votre Google Sheets
  SHEETS: {
    VEHICULES: 'Véhicules',
    UTILISATEURS: 'Utilisateurs',
    TYPES_VEHICULES: 'Types_Véhicules',
    PAIEMENTS: 'Paiements',
    PARAMETRES: 'Paramètres'
  }
};

/**
 * Fonction pour vérifier la configuration
 */
function verifierConfiguration() {
  try {
    if (CONFIG.SPREADSHEET_ID === '13vYDBbh5tM-StV6KJcrakpwf5wJj-qHlhQhEnF3G7Cc') {
      return {
        success: false,
        message: 'ERREUR: V<PERSON> de<PERSON> configurer l\'ID du Google Sheets dans CONFIG.SPREADSHEET_ID'
      };
    }

    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheets = ss.getSheets();
    const sheetNames = sheets.map(sheet => sheet.getName());

    // Vérifier que toutes les feuilles existent
    const missingSheets = [];
    Object.values(CONFIG.SHEETS).forEach(sheetName => {
      if (!sheetNames.includes(sheetName)) {
        missingSheets.push(sheetName);
      }
    });

    if (missingSheets.length > 0) {
      return {
        success: false,
        message: `Feuilles manquantes: ${missingSheets.join(', ')}`
      };
    }

    return {
      success: true,
      message: 'Configuration correcte',
      sheets: sheetNames
    };
  } catch (error) {
    return {
      success: false,
      message: 'Erreur d\'accès au Google Sheets: ' + error.toString()
    };
  }
}

/**
 * Fonction principale pour servir l'application web
 */
function doGet(e) {
  const page = e.parameter.page || 'login';
  
  switch(page) {
    case 'login':
      return HtmlService.createTemplateFromFile('login').evaluate()
        .setTitle('Gestion Fourrière - Connexion')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
    
    case 'dashboard':
      return HtmlService.createTemplateFromFile('dashboard').evaluate()
        .setTitle('Gestion Fourrière - Dashboard')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
    
    case 'entrees-sorties':
      return HtmlService.createTemplateFromFile('entrees-sorties').evaluate()
        .setTitle('Gestion Fourrière - Entrées/Sorties')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
    
    case 'paiements':
      return HtmlService.createTemplateFromFile('paiements').evaluate()
        .setTitle('Gestion Fourrière - Paiements')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
    
    case 'parametres':
      return HtmlService.createTemplateFromFile('parametres').evaluate()
        .setTitle('Gestion Fourrière - Paramètres')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
    
    default:
      return HtmlService.createTemplateFromFile('login').evaluate()
        .setTitle('Gestion Fourrière - Connexion')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
  }
}

/**
 * Fonction pour inclure des fichiers CSS/JS
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

/**
 * Authentification utilisateur
 */
function authentifierUtilisateur(email, motDePasse) {
  try {
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName(CONFIG.SHEETS.UTILISATEURS);
    const data = sheet.getDataRange().getValues();
    
    for (let i = 1; i < data.length; i++) {
      if (data[i][1] === email && data[i][2] === motDePasse && data[i][4] === true) {
        return {
          success: true,
          user: {
            id: data[i][0],
            email: data[i][1],
            nom: data[i][3],
            role: data[i][5]
          }
        };
      }
    }
    
    return { success: false, message: 'Email ou mot de passe incorrect' };
  } catch (error) {
    return { success: false, message: 'Erreur de connexion: ' + error.toString() };
  }
}

/**
 * Obtenir la liste des véhicules
 */
function obtenirVehicules() {
  try {
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName(CONFIG.SHEETS.VEHICULES);
    const data = sheet.getDataRange().getValues();
    
    const vehicules = [];
    for (let i = 1; i < data.length; i++) {
      vehicules.push({
        id: data[i][0],
        immatriculation: data[i][1],
        type: data[i][2],
        marque: data[i][3],
        modele: data[i][4],
        dateEntree: data[i][5],
        dateSortie: data[i][6],
        statut: data[i][7],
        montantDu: data[i][8],
        montantPaye: data[i][9]
      });
    }
    
    return { success: true, data: vehicules };
  } catch (error) {
    return { success: false, message: 'Erreur lors de la récupération des véhicules: ' + error.toString() };
  }
}

/**
 * Ajouter un nouveau véhicule
 */
function ajouterVehicule(vehiculeData) {
  try {
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName(CONFIG.SHEETS.VEHICULES);
    
    const id = Utilities.getUuid();
    const dateEntree = new Date();
    const tarif = calculerTarif(vehiculeData.type, 0); // 0 jours pour l'entrée
    
    sheet.appendRow([
      id,
      vehiculeData.immatriculation,
      vehiculeData.type,
      vehiculeData.marque,
      vehiculeData.modele,
      dateEntree,
      '', // Date de sortie vide
      'En fourrière',
      tarif,
      0 // Montant payé initial
    ]);
    
    return { success: true, message: 'Véhicule ajouté avec succès', id: id };
  } catch (error) {
    return { success: false, message: 'Erreur lors de l\'ajout du véhicule: ' + error.toString() };
  }
}

/**
 * Calculer le tarif selon le type de véhicule et le nombre de jours
 */
function calculerTarif(typeVehicule, nbJours) {
  try {
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName(CONFIG.SHEETS.TYPES_VEHICULES);
    const data = sheet.getDataRange().getValues();
    
    let tarifJournalier = 50; // Tarif par défaut
    
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === typeVehicule) {
        tarifJournalier = data[i][1];
        break;
      }
    }
    
    return tarifJournalier * Math.max(1, nbJours); // Minimum 1 jour
  } catch (error) {
    console.error('Erreur calcul tarif:', error);
    return 50; // Tarif par défaut en cas d'erreur
  }
}

/**
 * Sortir un véhicule de la fourrière
 */
function sortirVehicule(vehiculeId) {
  try {
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName(CONFIG.SHEETS.VEHICULES);
    const data = sheet.getDataRange().getValues();
    
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === vehiculeId) {
        const dateEntree = new Date(data[i][5]);
        const dateSortie = new Date();
        const nbJours = Math.ceil((dateSortie - dateEntree) / (1000 * 60 * 60 * 24));
        const montantTotal = calculerTarif(data[i][2], nbJours);
        
        // Mettre à jour la ligne
        sheet.getRange(i + 1, 7).setValue(dateSortie); // Date de sortie
        sheet.getRange(i + 1, 8).setValue('Sorti'); // Statut
        sheet.getRange(i + 1, 9).setValue(montantTotal); // Montant dû
        
        return { 
          success: true, 
          message: 'Véhicule sorti avec succès',
          nbJours: nbJours,
          montantTotal: montantTotal
        };
      }
    }
    
    return { success: false, message: 'Véhicule non trouvé' };
  } catch (error) {
    return { success: false, message: 'Erreur lors de la sortie du véhicule: ' + error.toString() };
  }
}

/**
 * Enregistrer un paiement
 */
function enregistrerPaiement(vehiculeId, montant, methodePaiement) {
  try {
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    
    // Ajouter le paiement dans la feuille Paiements
    const paiementsSheet = ss.getSheetByName(CONFIG.SHEETS.PAIEMENTS);
    const paiementId = Utilities.getUuid();
    
    paiementsSheet.appendRow([
      paiementId,
      vehiculeId,
      montant,
      methodePaiement,
      new Date(),
      'Validé'
    ]);
    
    // Mettre à jour le montant payé dans la feuille Véhicules
    const vehiculesSheet = ss.getSheetByName(CONFIG.SHEETS.VEHICULES);
    const data = vehiculesSheet.getDataRange().getValues();
    
    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === vehiculeId) {
        const montantActuel = data[i][9] || 0;
        const nouveauMontant = montantActuel + montant;
        vehiculesSheet.getRange(i + 1, 10).setValue(nouveauMontant);
        break;
      }
    }
    
    return { success: true, message: 'Paiement enregistré avec succès' };
  } catch (error) {
    return { success: false, message: 'Erreur lors de l\'enregistrement du paiement: ' + error.toString() };
  }
}

/**
 * Obtenir les types de véhicules
 */
function obtenirTypesVehicules() {
  try {
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName(CONFIG.SHEETS.TYPES_VEHICULES);
    const data = sheet.getDataRange().getValues();
    
    const types = [];
    for (let i = 1; i < data.length; i++) {
      types.push({
        nom: data[i][0],
        tarifJournalier: data[i][1],
        description: data[i][2]
      });
    }
    
    return { success: true, data: types };
  } catch (error) {
    return { success: false, message: 'Erreur lors de la récupération des types: ' + error.toString() };
  }
}

/**
 * Obtenir les statistiques du dashboard
 */
function obtenirStatistiques() {
  try {
    // Vérifier la configuration d'abord
    const configCheck = verifierConfiguration();
    if (!configCheck.success) {
      return configCheck;
    }

    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const vehiculesSheet = ss.getSheetByName(CONFIG.SHEETS.VEHICULES);
    const data = vehiculesSheet.getDataRange().getValues();

    // Si pas de données (seulement l'en-tête)
    if (data.length <= 1) {
      return {
        success: true,
        data: {
          vehiculesEnFourriere: 0,
          vehiculesSortis: 0,
          recetteTotal: 0,
          totalVehicules: 0
        }
      };
    }

    let vehiculesEnFourriere = 0;
    let vehiculesSortis = 0;
    let recetteTotal = 0;

    for (let i = 1; i < data.length; i++) {
      if (data[i][7] === 'En fourrière') {
        vehiculesEnFourriere++;
      } else if (data[i][7] === 'Sorti') {
        vehiculesSortis++;
        recetteTotal += data[i][9] || 0; // Montant payé
      }
    }

    return {
      success: true,
      data: {
        vehiculesEnFourriere,
        vehiculesSortis,
        recetteTotal,
        totalVehicules: vehiculesEnFourriere + vehiculesSortis
      }
    };
  } catch (error) {
    return { success: false, message: 'Erreur lors de la récupération des statistiques: ' + error.toString() };
  }
}

/**
 * Fonction de test pour vérifier que tout fonctionne
 */
function testerConfiguration() {
  console.log('=== Test de Configuration ===');

  const configResult = verifierConfiguration();
  console.log('Configuration:', configResult);

  if (configResult.success) {
    console.log('=== Test des Statistiques ===');
    const statsResult = obtenirStatistiques();
    console.log('Statistiques:', statsResult);

    console.log('=== Test des Véhicules ===');
    const vehiculesResult = obtenirVehicules();
    console.log('Véhicules:', vehiculesResult);

    console.log('=== Test des Types ===');
    const typesResult = obtenirTypesVehicules();
    console.log('Types:', typesResult);
  }

  return configResult;
}
