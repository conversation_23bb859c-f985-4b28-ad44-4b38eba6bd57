# 🚀 Instructions d'Installation - Application Gestion Fourrière

## ⚠️ IMPORTANT - Résolution du Dashboard Vide

Si votre dashboard est vide après la connexion, c'est probablement dû à une configuration incomplète. Suivez ces étapes **dans l'ordre** :

## 📋 Étape 1 : <PERSON><PERSON><PERSON> le Google Sheets

1. **Allez sur [sheets.google.com](https://sheets.google.com)**
2. **Créez un nouveau document** (bouton "+" ou "Nouveau")
3. **Renommez le document** : "Base_Donnees_Fourriere"

### Créer les feuilles nécessaires :

#### Feuille 1 : "Véhicules"
1. Renommez la première feuille en "Véhicules"
2. Ajoutez ces en-têtes dans la ligne 1 :
```
A1: ID
B1: Immatriculation  
C1: Type
D1: Marque
E1: Modele
F1: Date_Entree
G1: Date_Sortie
H1: Statut
I1: Montant_Du
J1: Montant_Paye
```

3. A<PERSON><PERSON><PERSON> quelques données de test (ligne 2) :
```
A2: v001
B2: AB-123-CD
C2: Voiture
D2: Peugeot
E2: 308
F2: 2024-01-15
G2: (vide)
H2: En fourrière
I2: 150
J2: 0
```

#### Feuille 2 : "Utilisateurs"
1. Créez une nouvelle feuille : clic droit sur l'onglet → "Insérer une feuille"
2. Nommez-la "Utilisateurs"
3. Ajoutez ces en-têtes :
```
A1: ID
B1: Email
C1: Mot_De_Passe
D1: Nom
E1: Actif
F1: Role
```

4. Ajoutez l'utilisateur admin (ligne 2) :
```
A2: u001
B2: <EMAIL>
C2: admin123
D2: Administrateur
E2: TRUE
F2: Admin
```

#### Feuille 3 : "Types_Véhicules"
1. Créez une nouvelle feuille "Types_Véhicules"
2. Ajoutez ces en-têtes :
```
A1: Type
B1: Tarif_Journalier
C1: Description
```

3. Ajoutez quelques types (lignes 2-4) :
```
A2: Voiture    B2: 50    C2: Véhicule léger standard
A3: Moto       B3: 25    C3: Deux roues motorisé
A4: Camion     B4: 100   C4: Véhicule utilitaire lourd
```

#### Feuille 4 : "Paiements"
1. Créez une nouvelle feuille "Paiements"
2. Ajoutez ces en-têtes :
```
A1: ID
B1: Vehicule_ID
C1: Montant
D1: Methode_Paiement
E1: Date_Paiement
F1: Statut
```

#### Feuille 5 : "Paramètres"
1. Créez une nouvelle feuille "Paramètres"
2. Ajoutez ces en-têtes :
```
A1: Parametre
B1: Valeur
C1: Description
```

## 📋 Étape 2 : Récupérer l'ID du Google Sheets

1. **Dans votre Google Sheets**, regardez l'URL dans la barre d'adresse
2. **Copiez la partie entre `/d/` et `/edit`**

Exemple d'URL :
```
https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit
```
L'ID est : `1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms`

## 📋 Étape 3 : Créer le projet Google Apps Script

1. **Allez sur [script.google.com](https://script.google.com)**
2. **Créez un nouveau projet** (bouton "Nouveau projet")
3. **Renommez le projet** : "Gestion_Fourriere"

## 📋 Étape 4 : Ajouter les fichiers

### Fichier Code.gs
1. **Remplacez le contenu** du fichier Code.gs par le contenu du fichier `Code.gs` fourni
2. **IMPORTANT** : Trouvez cette ligne (ligne 9) :
```javascript
SPREADSHEET_ID: 'VOTRE_ID_SPREADSHEET',
```
3. **Remplacez** `VOTRE_ID_SPREADSHEET` par l'ID que vous avez copié à l'étape 2
4. **Sauvegardez** (Ctrl+S)

### Fichiers HTML
1. **Ajoutez chaque fichier HTML** :
   - Cliquez sur le "+" à côté de "Fichiers"
   - Choisissez "HTML"
   - Nommez le fichier (login, dashboard, entrees-sorties, paiements, parametres)
   - Copiez le contenu correspondant
   - Sauvegardez

## 📋 Étape 5 : Tester la Configuration

1. **Dans Google Apps Script**, cliquez sur la fonction `testerConfiguration`
2. **Exécutez la fonction** (bouton "Exécuter")
3. **Autorisez les permissions** si demandé
4. **Vérifiez les logs** (Affichage → Logs)

Si tout est correct, vous devriez voir :
```
Configuration: {success: true, message: "Configuration correcte"}
```

## 📋 Étape 6 : Déployer l'Application

1. **Cliquez sur "Déployer"** → "Nouveau déploiement"
2. **Choisissez le type** : "Application web"
3. **Configuration** :
   - Description : "Gestion Fourrière v1.0"
   - Exécuter en tant que : "Moi"
   - Qui a accès : "Tout le monde"
4. **Cliquez sur "Déployer"**
5. **Copiez l'URL** de l'application web

## 📋 Étape 7 : Tester l'Application

1. **Ouvrez l'URL** de votre application
2. **Connectez-vous** avec :
   - Email : `<EMAIL>`
   - Mot de passe : `admin123`
3. **Vérifiez** que le dashboard affiche les statistiques

## 🔧 Résolution des Problèmes

### Dashboard Vide ou Erreurs
1. **Vérifiez l'ID du Google Sheets** dans Code.gs
2. **Vérifiez les noms des feuilles** (respectez exactement les noms)
3. **Vérifiez les permissions** du Google Sheets
4. **Exécutez** la fonction `testerConfiguration()` dans Apps Script

### Erreur d'Autorisation
1. **Dans Google Apps Script** → Exécutez n'importe quelle fonction
2. **Autorisez** toutes les permissions demandées
3. **Redéployez** l'application

### Données Non Trouvées
1. **Vérifiez** que les feuilles ont des données (au moins les en-têtes)
2. **Vérifiez** l'orthographe des noms de feuilles
3. **Ajoutez** des données de test

## 📞 Support

Si vous rencontrez encore des problèmes :

1. **Ouvrez la console** du navigateur (F12) pour voir les erreurs JavaScript
2. **Vérifiez les logs** dans Google Apps Script
3. **Testez** chaque fonction individuellement dans Apps Script

## ✅ Checklist de Vérification

- [ ] Google Sheets créé avec 5 feuilles
- [ ] Données de test ajoutées
- [ ] ID du Google Sheets configuré dans Code.gs
- [ ] Tous les fichiers HTML ajoutés
- [ ] Fonction `testerConfiguration()` réussie
- [ ] Application déployée
- [ ] Connexion ré<NAME_EMAIL>
- [ ] Dashboard affiche les statistiques

Une fois toutes ces étapes complétées, votre application devrait fonctionner parfaitement !
