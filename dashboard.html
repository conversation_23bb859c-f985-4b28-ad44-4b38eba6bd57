<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Gestion Fourrière</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            min-height: 100vh;
        }

        /* Menu vertical */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 1rem;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .user-info {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .menu-item {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }

        .menu-item:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .menu-item.active {
            background-color: rgba(255,255,255,0.2);
            border-right: 3px solid white;
        }

        .menu-item i {
            margin-right: 0.5rem;
            width: 20px;
        }

        /* Contenu principal */
        .main-content {
            margin-left: 250px;
            flex: 1;
            padding: 2rem;
        }

        .header {
            background: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        /* Cartes de statistiques */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-card h3 {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .stat-card .icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .stat-card.vehicles { border-left: 4px solid #28a745; }
        .stat-card.exits { border-left: 4px solid #17a2b8; }
        .stat-card.revenue { border-left: 4px solid #ffc107; }
        .stat-card.total { border-left: 4px solid #6f42c1; }

        /* Tableau récent */
        .recent-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .recent-section h2 {
            color: #333;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f0f0f0;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status.en-fourriere {
            background-color: #fff3cd;
            color: #856404;
        }

        .status.sorti {
            background-color: #d4edda;
            color: #155724;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Menu vertical -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h2>🚗 Gestion Fourrière</h2>
            <div class="user-info" id="userInfo">
                Chargement...
            </div>
        </div>
        
        <a href="?page=dashboard" class="menu-item active">
            <i>📊</i> Dashboard
        </a>
        <a href="?page=entrees-sorties" class="menu-item">
            <i>🚗</i> Entrées/Sorties
        </a>
        <a href="?page=paiements" class="menu-item">
            <i>💰</i> Paiements
        </a>
        <a href="?page=parametres" class="menu-item">
            <i>⚙️</i> Paramètres
        </a>
    </nav>

    <!-- Contenu principal -->
    <main class="main-content">
        <header class="header">
            <h1>Dashboard</h1>
            <button class="logout-btn" onclick="deconnecter()">
                Déconnexion
            </button>
        </header>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card vehicles">
                <div class="icon">🚗</div>
                <h3>Véhicules en fourrière</h3>
                <div class="number" id="vehiculesEnFourriere">-</div>
            </div>
            
            <div class="stat-card exits">
                <div class="icon">✅</div>
                <h3>Véhicules sortis</h3>
                <div class="number" id="vehiculesSortis">-</div>
            </div>
            
            <div class="stat-card revenue">
                <div class="icon">💰</div>
                <h3>Recettes totales</h3>
                <div class="number" id="recetteTotal">-</div>
            </div>
            
            <div class="stat-card total">
                <div class="icon">📈</div>
                <h3>Total véhicules</h3>
                <div class="number" id="totalVehicules">-</div>
            </div>
        </div>

        <!-- Véhicules récents -->
        <section class="recent-section">
            <h2>Véhicules récents</h2>
            <div class="table-container">
                <table id="vehiculesTable">
                    <thead>
                        <tr>
                            <th>Immatriculation</th>
                            <th>Type</th>
                            <th>Marque</th>
                            <th>Date d'entrée</th>
                            <th>Statut</th>
                            <th>Montant dû</th>
                        </tr>
                    </thead>
                    <tbody id="vehiculesTableBody">
                        <tr>
                            <td colspan="6" class="loading">Chargement des données...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <script>
        // Vérifier l'authentification
        function verifierAuth() {
            const user = sessionStorage.getItem('user');
            if (!user) {
                window.location.href = '?page=login';
                return null;
            }
            return JSON.parse(user);
        }

        // Déconnexion
        function deconnecter() {
            sessionStorage.removeItem('user');
            window.location.href = '?page=login';
        }

        // Charger les statistiques
        function chargerStatistiques() {
            // Afficher un indicateur de chargement
            document.getElementById('vehiculesEnFourriere').textContent = '...';
            document.getElementById('vehiculesSortis').textContent = '...';
            document.getElementById('recetteTotal').textContent = '...';
            document.getElementById('totalVehicules').textContent = '...';

            google.script.run
                .withSuccessHandler(function(result) {
                    if (result.success) {
                        const stats = result.data;
                        document.getElementById('vehiculesEnFourriere').textContent = stats.vehiculesEnFourriere;
                        document.getElementById('vehiculesSortis').textContent = stats.vehiculesSortis;
                        document.getElementById('recetteTotal').textContent = stats.recetteTotal.toFixed(2) + ' €';
                        document.getElementById('totalVehicules').textContent = stats.totalVehicules;
                    } else {
                        // Afficher l'erreur dans les statistiques
                        document.getElementById('vehiculesEnFourriere').textContent = 'Erreur';
                        document.getElementById('vehiculesSortis').textContent = 'Erreur';
                        document.getElementById('recetteTotal').textContent = 'Erreur';
                        document.getElementById('totalVehicules').textContent = 'Erreur';

                        // Afficher le message d'erreur
                        afficherErreurConfiguration(result.message);
                    }
                })
                .withFailureHandler(function(error) {
                    console.error('Erreur lors du chargement des statistiques:', error);
                    document.getElementById('vehiculesEnFourriere').textContent = 'Erreur';
                    document.getElementById('vehiculesSortis').textContent = 'Erreur';
                    document.getElementById('recetteTotal').textContent = 'Erreur';
                    document.getElementById('totalVehicules').textContent = 'Erreur';

                    afficherErreurConfiguration('Erreur de connexion: ' + error.message);
                })
                .obtenirStatistiques();
        }

        // Charger les véhicules
        function chargerVehicules() {
            google.script.run
                .withSuccessHandler(function(result) {
                    const tbody = document.getElementById('vehiculesTableBody');

                    if (result.success) {
                        const vehicules = result.data.slice(0, 10); // Afficher les 10 derniers

                        if (vehicules.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">Aucun véhicule trouvé</td></tr>';
                            return;
                        }

                        tbody.innerHTML = vehicules.map(vehicule => `
                            <tr>
                                <td>${vehicule.immatriculation}</td>
                                <td>${vehicule.type}</td>
                                <td>${vehicule.marque}</td>
                                <td>${new Date(vehicule.dateEntree).toLocaleDateString('fr-FR')}</td>
                                <td>
                                    <span class="status ${vehicule.statut === 'En fourrière' ? 'en-fourriere' : 'sorti'}">
                                        ${vehicule.statut}
                                    </span>
                                </td>
                                <td>${vehicule.montantDu ? vehicule.montantDu.toFixed(2) + ' €' : '-'}</td>
                            </tr>
                        `).join('');
                    } else {
                        tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: red;">Erreur: ' + result.message + '</td></tr>';
                    }
                })
                .withFailureHandler(function(error) {
                    document.getElementById('vehiculesTableBody').innerHTML =
                        '<tr><td colspan="6" style="text-align: center; color: red;">Erreur de chargement: ' + error.message + '</td></tr>';
                })
                .obtenirVehicules();
        }

        // Afficher une erreur de configuration
        function afficherErreurConfiguration(message) {
            const container = document.querySelector('.main-content');
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                background: #f8d7da;
                color: #721c24;
                padding: 1rem;
                border-radius: 5px;
                margin-bottom: 1rem;
                border: 1px solid #f5c6cb;
            `;
            errorDiv.innerHTML = `
                <h4>⚠️ Erreur de Configuration</h4>
                <p>${message}</p>
                <p><strong>Instructions:</strong></p>
                <ol>
                    <li>Créez un Google Sheets avec les feuilles: Véhicules, Utilisateurs, Types_Véhicules, Paiements, Paramètres</li>
                    <li>Copiez l'ID du Google Sheets (dans l'URL)</li>
                    <li>Remplacez 'VOTRE_ID_SPREADSHEET' dans Code.gs par votre ID</li>
                    <li>Redéployez l'application</li>
                </ol>
            `;
            container.insertBefore(errorDiv, container.firstChild.nextSibling);
        }

        // Initialisation
        window.onload = function() {
            const user = verifierAuth();
            if (user) {
                document.getElementById('userInfo').textContent = user.nom + ' (' + user.role + ')';
                chargerStatistiques();
                chargerVehicules();
            }
        };
    </script>
</body>
</html>
