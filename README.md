# Application de Gestion de Fourrière

## 📋 Description
Application web développée avec Google Apps Script pour la gestion d'une fourrière automobile. L'application permet de gérer les entrées/sorties de véhicules, les paiements, et les paramètres du système.

## 🚀 Fonctionnalités

### ✅ Fonctionnalités Implémentées
- **Authentification** : Système de connexion sécurisé
- **Dashboard** : Vue d'ensemble avec statistiques
- **Gestion des véhicules** : Entrées et sorties
- **Gestion des paiements** : Enregistrement et suivi
- **Calcul automatique** : Tarifs selon le type et durée
- **Interface responsive** : Compatible mobile et desktop

### 🔧 Fonctionnalités à Développer
- Gestion complète des utilisateurs
- Paramètres avancés du système
- Historique détaillé des paiements
- Rapports et exports
- Notifications automatiques

## 📁 Structure du Projet

```
├── Code.gs                    # Fichier principal Google Apps Script
├── login.html                 # Page de connexion
├── dashboard.html             # Dashboard principal
├── entrees-sorties.html       # Gestion des entrées/sorties
├── paiements.html             # Gestion des paiements
├── parametres.html            # Page de paramètres
├── Base_Donnees_Fourriere.csv # Structure de la base de données
└── README.md                  # Documentation
```

## 🛠️ Installation et Configuration

### 1. Créer le projet Google Apps Script
1. Allez sur [script.google.com](https://script.google.com)
2. Créez un nouveau projet
3. Copiez le contenu de `Code.gs` dans le fichier principal
4. Ajoutez les fichiers HTML (login.html, dashboard.html, etc.)

### 2. Créer la base de données Google Sheets
1. Créez un nouveau Google Sheets
2. Créez les feuilles suivantes avec les colonnes indiquées :

#### Feuille "Véhicules"
```
ID | Immatriculation | Type | Marque | Modele | Date_Entree | Date_Sortie | Statut | Montant_Du | Montant_Paye
```

#### Feuille "Utilisateurs"
```
ID | Email | Mot_De_Passe | Nom | Actif | Role
```

#### Feuille "Types_Véhicules"
```
Type | Tarif_Journalier | Description
```

#### Feuille "Paiements"
```
ID | Vehicule_ID | Montant | Methode_Paiement | Date_Paiement | Statut
```

#### Feuille "Paramètres"
```
Parametre | Valeur | Description
```

### 3. Configuration
1. Dans `Code.gs`, remplacez `VOTRE_ID_SPREADSHEET` par l'ID de votre Google Sheets
2. Ajoutez des données de test dans les feuilles
3. Déployez l'application comme application web

### 4. Déploiement
1. Dans Google Apps Script, cliquez sur "Déployer" > "Nouveau déploiement"
2. Choisissez "Application web"
3. Définissez les autorisations appropriées
4. Copiez l'URL de déploiement

## 👥 Utilisateurs par Défaut

Ajoutez ces utilisateurs dans la feuille "Utilisateurs" :

```csv
u001,<EMAIL>,admin123,Administrateur,TRUE,Admin
u002,<EMAIL>,agent123,Agent Martin,TRUE,Agent
u003,<EMAIL>,agent456,Agent Dubois,TRUE,Agent
```

## 🚗 Types de Véhicules par Défaut

Ajoutez ces types dans la feuille "Types_Véhicules" :

```csv
Voiture,50,Véhicule léger standard
Moto,25,Deux roues motorisé
Camion,100,Véhicule utilitaire lourd
Camionnette,75,Véhicule utilitaire léger
Bus,150,Transport en commun
Remorque,30,Remorque ou caravane
```

## 💡 Utilisation

### Connexion
- Utilisez les identifiants par défaut pour vous connecter
- Email : `<EMAIL>`
- Mot de passe : `admin123`

### Gestion des Véhicules
1. **Nouvelle entrée** : Remplissez le formulaire avec les informations du véhicule
2. **Sortie** : Recherchez le véhicule par immatriculation et confirmez la sortie
3. **Liste** : Consultez tous les véhicules avec leurs statuts

### Gestion des Paiements
1. Recherchez le véhicule par immatriculation
2. Vérifiez le montant dû
3. Enregistrez le paiement avec la méthode choisie

### Paramètres
- Gérez les types de véhicules et leurs tarifs
- Configurez les paramètres généraux du système

## 🔧 Personnalisation

### Modifier les Tarifs
Modifiez les tarifs dans la feuille "Types_Véhicules" ou via l'interface de paramètres.

### Ajouter des Fonctionnalités
Le code est modulaire et peut être étendu facilement :
- Ajoutez de nouvelles fonctions dans `Code.gs`
- Créez de nouvelles pages HTML
- Modifiez les styles CSS selon vos besoins

## 🐛 Dépannage

### Erreurs Communes
1. **Erreur d'autorisation** : Vérifiez les permissions du script
2. **Données non trouvées** : Vérifiez l'ID du Google Sheets
3. **Erreur de connexion** : Vérifiez les identifiants utilisateur

### Support
Pour toute question ou problème :
1. Vérifiez la console de Google Apps Script pour les erreurs
2. Consultez la documentation Google Apps Script
3. Testez avec des données simples d'abord

## 📈 Améliorations Futures

- [ ] Système de notifications par email
- [ ] Génération de rapports PDF
- [ ] Interface d'administration avancée
- [ ] Intégration avec systèmes externes
- [ ] Application mobile native
- [ ] Sauvegarde automatique des données

## 📄 Licence

Ce projet est développé pour un usage éducatif et professionnel. Vous pouvez l'adapter selon vos besoins.

---

**Développé avec ❤️ pour la gestion efficace des fourrières automobiles**
