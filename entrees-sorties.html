<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Entrées/Sorties - Gestion Fourrière</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            min-height: 100vh;
        }

        /* Menu vertical (même style que dashboard) */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 1rem;
        }

        .sidebar-header h2 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .user-info {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .menu-item {
            display: block;
            padding: 1rem 1.5rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .menu-item:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .menu-item.active {
            background-color: rgba(255,255,255,0.2);
            border-right: 3px solid white;
        }

        .menu-item i {
            margin-right: 0.5rem;
            width: 20px;
        }

        /* Contenu principal */
        .main-content {
            margin-left: 250px;
            flex: 1;
            padding: 2rem;
        }

        .header {
            background: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        /* Onglets */
        .tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .tab.active {
            background: #667eea;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #e9ecef;
        }

        /* Contenu des onglets */
        .tab-content {
            display: none;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        /* Formulaires */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        /* Tableau */
        .table-container {
            overflow-x: auto;
            margin-top: 2rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .status {
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status.en-fourriere {
            background-color: #fff3cd;
            color: #856404;
        }

        .status.sorti {
            background-color: #d4edda;
            color: #155724;
        }

        .actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- Menu vertical -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h2>🚗 Gestion Fourrière</h2>
            <div class="user-info" id="userInfo">
                Chargement...
            </div>
        </div>
        
        <a href="?page=dashboard" class="menu-item">
            <i>📊</i> Dashboard
        </a>
        <a href="?page=entrees-sorties" class="menu-item active">
            <i>🚗</i> Entrées/Sorties
        </a>
        <a href="?page=paiements" class="menu-item">
            <i>💰</i> Paiements
        </a>
        <a href="?page=parametres" class="menu-item">
            <i>⚙️</i> Paramètres
        </a>
    </nav>

    <!-- Contenu principal -->
    <main class="main-content">
        <header class="header">
            <h1>Gestion des Entrées/Sorties</h1>
            <button class="logout-btn" onclick="deconnecter()">
                Déconnexion
            </button>
        </header>

        <!-- Onglets -->
        <div class="tabs">
            <button class="tab active" onclick="changerOnglet('entree')">
                Nouvelle Entrée
            </button>
            <button class="tab" onclick="changerOnglet('sortie')">
                Sortie de Véhicule
            </button>
            <button class="tab" onclick="changerOnglet('liste')">
                Liste des Véhicules
            </button>
        </div>

        <!-- Messages -->
        <div id="messageContainer"></div>

        <!-- Contenu Nouvelle Entrée -->
        <div id="entree" class="tab-content active">
            <h2>Enregistrer une nouvelle entrée</h2>
            <form id="entreeForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="immatriculation">Immatriculation *</label>
                        <input type="text" id="immatriculation" name="immatriculation" required 
                               placeholder="Ex: AB-123-CD" style="text-transform: uppercase;">
                    </div>
                    
                    <div class="form-group">
                        <label for="type">Type de véhicule *</label>
                        <select id="type" name="type" required>
                            <option value="">Sélectionner un type</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="marque">Marque *</label>
                        <input type="text" id="marque" name="marque" required placeholder="Ex: Peugeot">
                    </div>
                    
                    <div class="form-group">
                        <label for="modele">Modèle *</label>
                        <input type="text" id="modele" name="modele" required placeholder="Ex: 308">
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    Enregistrer l'entrée
                </button>
            </form>
        </div>

        <!-- Contenu Sortie -->
        <div id="sortie" class="tab-content">
            <h2>Sortie de véhicule</h2>
            <div class="form-group">
                <label for="rechercheVehicule">Rechercher un véhicule (par immatriculation)</label>
                <input type="text" id="rechercheVehicule" placeholder="Tapez l'immatriculation..." 
                       style="text-transform: uppercase;">
            </div>
            
            <div id="vehiculeInfo" style="display: none;">
                <h3>Informations du véhicule</h3>
                <div id="vehiculeDetails"></div>
                <button id="confirmerSortie" class="btn btn-success" style="margin-top: 1rem;">
                    Confirmer la sortie
                </button>
            </div>
        </div>

        <!-- Contenu Liste -->
        <div id="liste" class="tab-content">
            <h2>Liste des véhicules</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Immatriculation</th>
                            <th>Type</th>
                            <th>Marque/Modèle</th>
                            <th>Date d'entrée</th>
                            <th>Nb jours</th>
                            <th>Statut</th>
                            <th>Montant dû</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="vehiculesTableBody">
                        <tr>
                            <td colspan="8" class="loading">Chargement des données...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script>
        let vehiculesData = [];
        let typesVehicules = [];

        // Vérifier l'authentification
        function verifierAuth() {
            const user = sessionStorage.getItem('user');
            if (!user) {
                window.location.href = '?page=login';
                return null;
            }
            return JSON.parse(user);
        }

        // Déconnexion
        function deconnecter() {
            sessionStorage.removeItem('user');
            window.location.href = '?page=login';
        }

        // Changer d'onglet
        function changerOnglet(onglet) {
            // Masquer tous les contenus
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Désactiver tous les onglets
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Activer l'onglet sélectionné
            document.getElementById(onglet).classList.add('active');
            event.target.classList.add('active');
            
            // Charger les données si nécessaire
            if (onglet === 'liste') {
                chargerVehicules();
            }
        }

        // Afficher un message
        function afficherMessage(message, type = 'success') {
            const container = document.getElementById('messageContainer');
            container.innerHTML = `
                <div class="alert alert-${type}">
                    ${message}
                </div>
            `;
            
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Charger les types de véhicules
        function chargerTypesVehicules() {
            google.script.run
                .withSuccessHandler(function(result) {
                    if (result.success) {
                        typesVehicules = result.data;
                        const select = document.getElementById('type');
                        select.innerHTML = '<option value="">Sélectionner un type</option>';
                        
                        typesVehicules.forEach(type => {
                            select.innerHTML += `<option value="${type.nom}">${type.nom} (${type.tarifJournalier}€/jour)</option>`;
                        });
                    }
                })
                .withFailureHandler(function(error) {
                    console.error('Erreur lors du chargement des types:', error);
                })
                .obtenirTypesVehicules();
        }

        // Gérer le formulaire d'entrée
        document.getElementById('entreeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const vehiculeData = {
                immatriculation: formData.get('immatriculation').toUpperCase(),
                type: formData.get('type'),
                marque: formData.get('marque'),
                modele: formData.get('modele')
            };
            
            google.script.run
                .withSuccessHandler(function(result) {
                    if (result.success) {
                        afficherMessage('Véhicule enregistré avec succès!', 'success');
                        e.target.reset();
                    } else {
                        afficherMessage('Erreur: ' + result.message, 'danger');
                    }
                })
                .withFailureHandler(function(error) {
                    afficherMessage('Erreur lors de l\'enregistrement: ' + error.message, 'danger');
                })
                .ajouterVehicule(vehiculeData);
        });

        // Recherche de véhicule pour sortie
        document.getElementById('rechercheVehicule').addEventListener('input', function(e) {
            const immatriculation = e.target.value.toUpperCase();
            
            if (immatriculation.length >= 3) {
                const vehicule = vehiculesData.find(v => 
                    v.immatriculation.includes(immatriculation) && v.statut === 'En fourrière'
                );
                
                if (vehicule) {
                    const dateEntree = new Date(vehicule.dateEntree);
                    const nbJours = Math.ceil((new Date() - dateEntree) / (1000 * 60 * 60 * 24));
                    
                    document.getElementById('vehiculeDetails').innerHTML = `
                        <p><strong>Immatriculation:</strong> ${vehicule.immatriculation}</p>
                        <p><strong>Type:</strong> ${vehicule.type}</p>
                        <p><strong>Marque/Modèle:</strong> ${vehicule.marque} ${vehicule.modele}</p>
                        <p><strong>Date d'entrée:</strong> ${dateEntree.toLocaleDateString('fr-FR')}</p>
                        <p><strong>Nombre de jours:</strong> ${nbJours}</p>
                        <p><strong>Montant à payer:</strong> ${vehicule.montantDu}€</p>
                    `;
                    
                    document.getElementById('vehiculeInfo').style.display = 'block';
                    document.getElementById('confirmerSortie').onclick = () => confirmerSortie(vehicule.id);
                } else {
                    document.getElementById('vehiculeInfo').style.display = 'none';
                }
            } else {
                document.getElementById('vehiculeInfo').style.display = 'none';
            }
        });

        // Confirmer la sortie
        function confirmerSortie(vehiculeId) {
            if (confirm('Confirmer la sortie de ce véhicule?')) {
                google.script.run
                    .withSuccessHandler(function(result) {
                        if (result.success) {
                            afficherMessage(`Véhicule sorti avec succès! Montant total: ${result.montantTotal}€`, 'success');
                            document.getElementById('rechercheVehicule').value = '';
                            document.getElementById('vehiculeInfo').style.display = 'none';
                            chargerVehicules();
                        } else {
                            afficherMessage('Erreur: ' + result.message, 'danger');
                        }
                    })
                    .withFailureHandler(function(error) {
                        afficherMessage('Erreur lors de la sortie: ' + error.message, 'danger');
                    })
                    .sortirVehicule(vehiculeId);
            }
        }

        // Charger les véhicules
        function chargerVehicules() {
            google.script.run
                .withSuccessHandler(function(result) {
                    const tbody = document.getElementById('vehiculesTableBody');
                    
                    if (result.success) {
                        vehiculesData = result.data;
                        
                        if (vehiculesData.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="8" style="text-align: center;">Aucun véhicule trouvé</td></tr>';
                            return;
                        }
                        
                        tbody.innerHTML = vehiculesData.map(vehicule => {
                            const dateEntree = new Date(vehicule.dateEntree);
                            const nbJours = vehicule.dateSortie ? 
                                Math.ceil((new Date(vehicule.dateSortie) - dateEntree) / (1000 * 60 * 60 * 24)) :
                                Math.ceil((new Date() - dateEntree) / (1000 * 60 * 60 * 24));
                            
                            return `
                                <tr>
                                    <td>${vehicule.immatriculation}</td>
                                    <td>${vehicule.type}</td>
                                    <td>${vehicule.marque} ${vehicule.modele}</td>
                                    <td>${dateEntree.toLocaleDateString('fr-FR')}</td>
                                    <td>${nbJours}</td>
                                    <td>
                                        <span class="status ${vehicule.statut === 'En fourrière' ? 'en-fourriere' : 'sorti'}">
                                            ${vehicule.statut}
                                        </span>
                                    </td>
                                    <td>${vehicule.montantDu ? vehicule.montantDu.toFixed(2) + ' €' : '-'}</td>
                                    <td class="actions">
                                        ${vehicule.statut === 'En fourrière' ? 
                                            `<button class="btn btn-success btn-sm" onclick="confirmerSortie('${vehicule.id}')">Sortir</button>` : 
                                            '-'
                                        }
                                    </td>
                                </tr>
                            `;
                        }).join('');
                    } else {
                        tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: red;">Erreur: ' + result.message + '</td></tr>';
                    }
                })
                .withFailureHandler(function(error) {
                    document.getElementById('vehiculesTableBody').innerHTML = 
                        '<tr><td colspan="8" style="text-align: center; color: red;">Erreur de chargement: ' + error.message + '</td></tr>';
                })
                .obtenirVehicules();
        }

        // Initialisation
        window.onload = function() {
            const user = verifierAuth();
            if (user) {
                document.getElementById('userInfo').textContent = user.nom + ' (' + user.role + ')';
                chargerTypesVehicules();
                chargerVehicules();
            }
        };
    </script>
</body>
</html>
